"use client";

import { useMemo, useEffect } from "react";
import { Play, Pause } from "lucide-react";
import { useEditorStore } from "../../stores/editor";
import { useTimelineStore } from "../../stores/timeline";
import { Player } from "./player";
import { formatSecondsToTime } from "../../utils";
import { usePreviewStore } from "../../stores/preview";
import { cn } from "@/lib/utils";
import { EmptyState } from "../empty-state";

export function Preview() {
  const { videoClips, selectedClips } = useEditorStore();
  const { currentTime, setCurrentTime, previewTime, scrollToTime } =
    useTimelineStore();
  const { playMode, setPlayMode, isPlaying, setIsPlaying } = usePreviewStore();

  /**
   * 计算时间轴的总时长：找到最大的 endTime 作为总时长
   */
  const totalDuration = useMemo(() => {
    if (videoClips && videoClips.length > 0) {
      return Math.max(...videoClips.map((clip) => clip.endTime));
    }
    return 0;
  }, [videoClips]);

  /**
   * 当视频暂停时，自动滚动到当前指示线位置并居中显示
   */
  useEffect(() => {
    if (!isPlaying && currentTime >= 0) {
      // 添加小延迟确保暂停状态已经稳定
      const timeoutId = setTimeout(() => {
        scrollToTime(currentTime);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [isPlaying, currentTime, scrollToTime]);

  /**
   * 处理播放模式切换
   * @param mode - 播放模式："single" 表示播放当前切片，"all" 表示预览素材成片
   */
  const handlePlayMode = (mode: "single" | "all") => {
    // 如果当前已经是选中的模式，则切换回默认模式
    if (playMode === mode) {
      setPlayMode("default");
      return;
    }

    // 否则设置为新的播放模式
    setPlayMode(mode);
  };

  return (
    <div className="flex flex-col h-full w-[600px]">
      {/* 标题 */}
      <p className="flex items-center h-10 px-4 text-sm font-bold text-gray-700">
        预览
      </p>

      {/* 监视器 */}
      <div className="px-4">
        {!selectedClips || selectedClips.length === 0 ? (
          <div className="aspect-video rounded-lg flex items-center justify-center">
            <EmptyState
              title="暂无视频预览"
              description="请先在左侧选择视频片段"
              className="py-8"
            />
          </div>
        ) : (
          <Player
            currentTime={currentTime}
            isPlaying={isPlaying}
            onTimeUpdate={(time) => setCurrentTime(time)}
            previewTime={previewTime || undefined}
          />
        )}
      </div>

      {/* 控制器 */}
      <div className="bg-gray-100 p-4">
        <div className="relative flex items-center">
          {/* 左侧：时间显示 */}
          <p className="text-sm absolute left-0 text-gray-600 transition-all duration-300">
            {formatSecondsToTime(currentTime)} /{" "}
            {formatSecondsToTime(totalDuration)}
          </p>

          {/* 中间：播放按钮 */}
          <div className="flex-1 flex justify-center">
            <button
              onClick={() => setIsPlaying(!isPlaying)}
              className="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-all duration-300 shadow-lg hover:shadow-xl"
              disabled={!videoClips || videoClips.length === 0}
            >
              {isPlaying ? <Pause size={18} /> : <Play size={18} />}
            </button>
          </div>

          {/* 右侧：控制按钮组 */}
          <div className="absolute right-2">1x</div>
        </div>
      </div>

      {/* 播放模式 */}
      <div className="flex gap-2 my-4 px-4">
        <button
          className={cn(
            "px-3 py-1 text-sm rounded transition-all duration-200",
            !selectedClips || selectedClips.length === 0
              ? "bg-gray-100 border border-gray-200 text-gray-400 cursor-not-allowed"
              : playMode === "single"
              ? "border border-blue-500 bg-blue-50 text-blue-700"
              : "bg-white border border-gray-300 text-gray-600 hover:bg-gray-50"
          )}
          onClick={() => handlePlayMode("single")}
          disabled={!selectedClips || selectedClips.length === 0}
        >
          播放当前切片
        </button>
        <button
          className={cn(
            "px-3 py-1 text-sm rounded transition-all duration-200",
            !selectedClips || selectedClips.length === 0
              ? "bg-gray-100 border border-gray-200 text-gray-400 cursor-not-allowed"
              : playMode === "all"
              ? "border border-blue-500 bg-blue-50 text-blue-700"
              : "bg-white border border-gray-300 text-gray-600 hover:bg-gray-50"
          )}
          onClick={() => handlePlayMode("all")}
          disabled={!selectedClips || selectedClips.length === 0}
        >
          预览素材成片
        </button>
      </div>
    </div>
  );
}
